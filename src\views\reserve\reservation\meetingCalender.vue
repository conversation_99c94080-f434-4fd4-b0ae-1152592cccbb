<template>
  <div class="app-container">
    <!-- 顶部工具栏 -->
    <div class="calendar-toolbar">
      <div class="toolbar-left">
        <el-select 
          v-model="selectedRoomId" 
          placeholder="选择会议室" 
          @change="loadEvents"
          style="width: 200px; margin-right: 20px;"
        >
          <el-option
            v-for="room in availableRooms"
            :key="room.roomId"
            :label="room.roomName"
            :value="room.roomId"
          />
        </el-select>
        
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新建预约
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button-group>
          <el-button @click="goToday">今天</el-button>
          <el-button @click="goPrev">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <el-button @click="goNext">
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </el-button-group>
        
        <span class="current-date">{{ currentDateTitle }}</span>
        
        <el-button-group style="margin-left: 20px;">
          <el-button 
            :type="currentView === 'dayGridMonth' ? 'primary' : ''"
            @click="changeView('dayGridMonth')"
          >
            月
          </el-button>
          <el-button 
            :type="currentView === 'timeGridWeek' ? 'primary' : ''"
            @click="changeView('timeGridWeek')"
          >
            周
          </el-button>
          <el-button 
            :type="currentView === 'timeGridDay' ? 'primary' : ''"
            @click="changeView('timeGridDay')"
          >
            日
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 日历主体 -->
    <div class="calendar-main">
      <!-- 右侧日历 -->
      <div class="calendar-content">
        <FullCalendar
          ref="calendarRef"
          :options="calendarOptions"
        />
      </div>
    </div>

    <!-- 新建/编辑预约对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="reservationRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会议室" prop="roomId">
          <el-select v-model="form.roomId" placeholder="请选择会议室" clearable style="width: 100%">
            <el-option
              v-for="room in availableRooms"
              :key="room.roomId"
              :label="room.roomName"
              :value="room.roomId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请人ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入申请人ID" disabled />
        </el-form-item>
        <el-form-item label="申请人姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入申请人姓名" disabled />
        </el-form-item>
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" disabled />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入部门名称" disabled />
        </el-form-item>
        <el-form-item label="会议主题" prop="meetingTitle">
          <el-input v-model="form.meetingTitle" placeholder="请输入会议主题" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
            placeholder="请选择会议开始时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
            placeholder="请选择会议结束时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="参会人员" prop="attendees">
          <el-input v-model="form.attendees" type="textarea" placeholder="请输入参会人员" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 事件详情对话框 -->
    <el-dialog
      title="预约详情"
      v-model="showDetailDialog"
      width="500px"
    >
      <div v-if="selectedEvent" class="event-detail">
        <el-descriptions :column="1" border>
            <el-descriptions-item label="会议主题">
                {{ selectedEvent.title }}
            </el-descriptions-item>
            <el-descriptions-item label="会议室">
                {{ getRoomName(selectedEvent.extendedProps.roomId) }}
            </el-descriptions-item>
            <el-descriptions-item label="时间">
                {{ formatEventTime(selectedEvent) }}
            </el-descriptions-item>
            <el-descriptions-item label="申请人">
                {{ selectedEvent.extendedProps.applicant || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="参会人员">
                {{ selectedEvent.extendedProps.attendees || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(selectedEvent.extendedProps.status)">
                {{ getStatusText(selectedEvent.extendedProps.status) }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="描述">
                {{ selectedEvent.extendedProps.description || '无' }}
            </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <!-- <el-button type="primary" @click="editCurrentEvent" v-if="canEdit(selectedEvent)">
            编辑
          </el-button>
          <el-button type="danger" @click="deleteCurrentEvent" v-if="canEdit(selectedEvent)">
            删除
          </el-button> -->
        </div>
      </template>
    </el-dialog>
    
    <!-- 日视图事件对话框 -->
    <el-dialog
    :title="`${selectedDate} 的预约`"
    v-model="showDayDialog"
    width="500px"
    class="day-events-dialog"
    >
    <div class="day-events-list">
        <div 
        v-for="event in dayEvents" 
        :key="event.id"
        class="event-item"
        @click="handleEventClick({ event })"
        >
        <div class="event-time">
            {{ formatEventTimeInDialog(event) }}
        </div>
        <div class="event-title">{{ event.title }}</div>
        <div class="event-room">{{ getRoomName(event.extendedProps?.roomId) }}</div>
        </div>
    </div>
    </el-dialog>

  </div>
</template>

<script setup name="RoomCalendar">
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue'
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import { listRoom } from "@/api/search/room"
import { getUserProfile } from "@/api/system/user"
import  useUserStore  from "@/store/modules/user"
import { 
  listReservation, 
  addReservation, 
  updateReservation, 
  delReservation ,
  myReservationList
} from "@/api/reserve/reservation"

const { proxy } = getCurrentInstance()

// 响应式数据
const calendarRef = ref()
const roomList = ref([])
const selectedRoomId = ref(null)
const showDetailDialog = ref(false)
const selectedEvent = ref(null)
const currentView = ref('dayGridMonth')
const currentDateTitle = ref('')
const userStore = useUserStore()

// 新的对话框相关数据
const reservationRef = ref()
const open = ref(false)
const title = ref("")

//仅显示 status 为 1 的会议室
const availableRooms = computed(() => {
  console.log(roomList.value)
  return roomList.value.filter(room => room.status === 1)
})

// 表单数据
const form = reactive({
  reservationId: null,
  roomId: null,
  userId: null,
  userName: "",
  deptId: null,
  deptName: "",
  meetingTitle: "",
  startTime: null,
  endTime: null,
  attendees: "",
  status: 1,
  remark: ""
})

// FullCalendar 配置
const calendarOptions = ref({
    plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
    initialView: 'dayGridMonth',
    locale: 'zh-cn',
    headerToolbar: false,
    editable: false,
    eventResizable: false,  // 明确禁用调整大小
    eventDragMinDistance: 0, // 禁用拖动
    selectable: true,
    selectMirror: true,
    dayMaxEvents: 3,
    weekends: true,
    slotMinTime: '08:00:00',
    slotMaxTime: '22:00:00',
    height: 'auto',
    events: [],
    
    // 事件处理
    select: handleDateSelect,
    eventClick: handleEventClick,
    // eventDrop: handleEventDrop,
    eventResize: handleEventResize,
    datesSet: handleDatesSet,
    
    // 自定义事件渲染
    eventContent: function(arg) {
        return {
        html: `
            <div class="custom-event">
            <div class="event-time">${arg.timeText}</div>
            <div class="event-title">${arg.event.title}</div>
            </div>
        `
        }
    },
    
    dayHeaderFormat: { weekday: 'short' },
    eventDisplay: 'block',
    eventBackgroundColor: '#409EFF',
    eventBorderColor: '#409EFF',
    eventTextColor: '#fff',

    moreLinkClick: function(info) {
    showDayEventsDialog(info)
    return false
  }
})

// 事件时间格式化函数
function formatEventTimeInDialog(event) {
  if (!event.start) return '全天'
  const date = typeof event.start === 'string' ? new Date(event.start) : event.start
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

// 状态管理
const showDayDialog = ref(false)
const dayEvents = ref([])
const selectedDate = ref('')

// 自定义更多事件处理
function showDayEventsDialog(info) {
  console.log('More link clicked:', info)
  
  selectedDate.value = info.date.toLocaleDateString('zh-CN')
  dayEvents.value = info.allSegs ? info.allSegs.map(seg => seg.event) : []
  showDayDialog.value = true
  
  console.log(`显示 ${selectedDate.value} 的 ${dayEvents.value.length} 个事件`)
}

// 表单验证规则
const rules = {
  roomId: [{ required: true, message: "会议室不能为空", trigger: "change" }],
  meetingTitle: [{ required: true, message: "会议主题不能为空", trigger: "blur" }],
  startTime: [{ required: true, message: "开始时间不能为空", trigger: "change" }],
  endTime: [{ required: true, message: "结束时间不能为空", trigger: "change" }],
  attendees: [{ required: true, message: "参会人员不能为空", trigger: "blur" }]
}

// 禁用过去日期
function disabledDate(time) {
  return time.getTime() < Date.now() - 8.64e7
}

// 初始化
onMounted(async () => {
  await loadUserInfo()
  loadRooms()
})


// 加载用户信息
async function loadUserInfo() {
  try {
    const response = await getUserProfile()
    const userInfo = response.data || response
    
    form.userId = userInfo.userId
    form.userName = userInfo.userName || userInfo.nickName
    form.deptId = userInfo.deptId
    form.deptName = userInfo.dept?.deptName || ""
    
    console.log('当前用户信息:', {
      userId: form.userId,
      userName: form.userName,
      deptId: form.deptId,
      deptName: form.deptName
    })
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}


// 加载会议室列表
async function loadRooms() {
  try {
    const response = await listRoom({ pageNum: 1, pageSize: 100 })
    roomList.value = response.rows || []
    if (roomList.value.length > 0 && !selectedRoomId.value) {
      selectedRoomId.value = roomList.value[0].roomId
      loadEvents()
    }
  } catch (error) {
    console.error('加载会议室列表失败:', error)
  }
}

// 状态映射
const statusMap = {
  '已取消': 0,
  '待审核': 1,
  '已通过': 2,
  '已拒绝': 3,
  '已完成': 4
}

const reverseStatusMap = {
  0: '已取消',
  1: '待审核',
  2: '已通过',
  3: '已拒绝',
  4: '已完成'
}



// 加载会议室预约列表
async function loadEvents() {
  try {
    const response1 = await listReservation({
      pageNum: 1,
      pageSize: 1000,
      roomId: selectedRoomId.value,
      status: 2
    })

    const response2 = await myReservationList({
      pageNum: 1,
      pageSize: 1000,
      roomId: selectedRoomId.value,
      userId: userStore.id
    })

    // 合并数据并去重
    const mergedRows  = [
      ...(response1.rows || []),
      ...(response2.rows || [])
    ].filter((item, index, self) => {
      // 根据reservationId去重
      return self.findIndex(i => i.reservationId === item.reservationId) === index;
    });

    const response = {
        rows: mergedRows,
        total:mergedRows.length,
        pageSize:1000,
        pageNum:1,
        source:'merged'
    }
    
    // 接使用合并后的数据，不要再访问 .rows
    const events = (response.rows || []).map((reservation) => {
      return {
        id: reservation.reservationId,
        title: reservation.meetingTitle,
        start: reservation.startTime,
        end: reservation.endTime,
        backgroundColor: getEventColor(reservation.status),
        borderColor: getEventColor(reservation.status),
        textColor: '#fff',
        extendedProps: {
            reservationId: reservation.reservationId,
            roomId: reservation.roomId,
            applicant: reservation.userName,
            attendees: reservation.attendees,
            status: reverseStatusMap[reservation.status],
            description: reservation.remark
        }
      }
    })
    
    console.log('合并后的完整数据:', response)
    console.log('处理后的事件数据:', events)
    calendarOptions.value.events = events
    
  } catch (error) {
    console.error('加载日历事件失败:', error)
  }
}


// 新增按钮处理
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会议室预约申请"
}

// 日期选择处理
function handleDateSelect(selectInfo) {
  console.log('selectInfo:', selectInfo)
  
  let startTime, endTime;
  
  if (selectInfo.allDay) {
    const startDate = new Date(selectInfo.start);
    const endDate = new Date(startDate); 
    
    startDate.setHours(8, 0, 0, 0);
    endDate.setHours(10, 0, 0, 0);
    
    startTime = formatDateTime(startDate);
    endTime = formatDateTime(endDate);
  } else {
    startTime = formatDateTime(selectInfo.start);
    endTime = formatDateTime(selectInfo.end);
  }
  
  reset()
  form.startTime = startTime;
  form.endTime = endTime;
  form.roomId = selectedRoomId.value;
  
  open.value = true
  title.value = "添加会议室预约申请"
}

// 格式化函数
function formatDateTime(date) {
  if (!date) return null;
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 事件点击处理
function handleEventClick(clickInfo) {
  selectedEvent.value = clickInfo.event
  showDetailDialog.value = true
}

// 事件拖拽处理
// async function handleEventDrop(dropInfo) {
//   await updateEventTime(
//     dropInfo.event.extendedProps.reservationId,
//     dropInfo.event.startStr,
//     dropInfo.event.endStr
//   )
// }

// 事件调整大小处理
async function handleEventResize(resizeInfo) {
  await updateEventTime(
    resizeInfo.event.extendedProps.reservationId,
    resizeInfo.event.startStr,
    resizeInfo.event.endStr
  )
}

// 日期范围变化处理
function handleDatesSet(dateInfo) {
  currentDateTitle.value = dateInfo.view.title
  currentView.value = dateInfo.view.type
}

// 工具栏操作
function goToday() {
  calendarRef.value.getApi().today()
}

function goPrev() {
  calendarRef.value.getApi().prev()
}

function goNext() {
  calendarRef.value.getApi().next()
}

function changeView(viewName) {
  calendarRef.value.getApi().changeView(viewName)
  currentView.value = viewName
}

// 提交表单
async function submitForm() {
  try {
    await proxy.$refs.reservationRef.validate()
    
    if (!form.userId) {
      proxy.$modal.msgError('无法获取用户信息，请重新登录')
      return
    }
    
    //如果会议室不可用直接退出
    // 正确：通过 roomId 查找对应的会议室
    const selectedRoom = roomList.value.find(room => room.roomId === form.roomId)

    // 先判断是否找到会议室，再检查状态
    if (!selectedRoom) {
      proxy.$modal.msgError('未找到选中的会议室')
      return
    }
    
    if (selectedRoom.status !== 1) { // 假设 1 代表可用状态
      proxy.$modal.msgError('当前会议室不可用')
      return
    }
    
    const formData = { ...form }
    
    if (form.reservationId != null) {
      await updateReservation(formData)
      proxy.$modal.msgSuccess("修改成功")
    } else {
      await addReservation(formData)
      proxy.$modal.msgSuccess("新增成功")
    }
    
    open.value = false
    loadEvents()
    
  } catch (error) {
    console.error('提交失败:', error)
    proxy.$modal.msgError('提交失败，请重试')
  }
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  Object.assign(form, {
    reservationId: null,
    roomId: null,
    meetingTitle: "",
    startTime: null,
    endTime: null,
    attendees: "",
    status: 1,
    remark: ""
  })
  // 重新加载用户信息
  loadUserInfo()
  proxy.$refs.reservationRef?.resetFields()
}

// 编辑当前事件
// function editCurrentEvent() {
//   if (!selectedEvent.value) return
  
//   const event = selectedEvent.value
//   reset()
  
//   Object.assign(form, {
//     reservationId: event.extendedProps.reservationId,
//     roomId: event.extendedProps.roomId,
//     meetingTitle: event.title,
//     startTime: event.startStr,
//     endTime: event.endStr,
//     attendees: event.extendedProps.attendees,
//     remark: event.extendedProps.description,
//     status: statusMap[event.extendedProps.status] || 1
//   })
  
//   showDetailDialog.value = false
//   open.value = true
//   title.value = "修改会议室预约申请"
// }

// 删除当前事件
// async function deleteCurrentEvent() {
//   if (!selectedEvent.value) return
  
//   try {
//     await proxy.$modal.confirm(`确定要删除预约"${selectedEvent.value.title}"吗？`)
//     await delReservation(selectedEvent.value.extendedProps.reservationId)
//     proxy.$modal.msgSuccess('删除成功')
//     showDetailDialog.value = false
//     loadEvents()
//   } catch (error) {
//     console.error('删除失败:', error)
//   }
// }

// 更新事件时间
async function updateEventTime(reservationId, startTime, endTime) {
  try {
    await updateReservation({
      reservationId: reservationId,
      startTime: startTime,
      endTime: endTime
    })
    proxy.$modal.msgSuccess('时间更新成功')
  } catch (error) {
    console.error('更新时间失败:', error)
    loadEvents()
  }
}

// 工具函数
function getRoomName(roomId) {
  const room = roomList.value.find(r => r.roomId === roomId)
  return room ? room.roomName : '未知会议室'
}

function formatEventTime(event) {
  const start = new Date(event.start).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
  const end = new Date(event.end).toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
  return `${start} - ${end}`
}

function getStatusType(status) {
  const map = {
    '已取消': 'info',
    '待审核': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger',
    '已完成': 'primary'
  }
  return map[status] || 'info';
}

function getStatusText(status) {
  const map = {
    '已取消': '已取消',
    '待审核': '待审核',
    '已通过': '已通过',
    '已拒绝': '已拒绝',
    '已完成': '已完成'
  }
  return map[status] || '未知';
}

function getEventColor(status) {
  const map = {
    0: '#909399',  // 已取消
    1: '#E6A23C',  // 待审核
    2: '#67C23A',  // 已通过
    3: '#F56C6C',  // 已拒绝
    4: '#409EFF'   // 已完成
  }
  return map[status] || '#409EFF';
}

function canEdit(event) {
  if (!event) return false
  return ['待审核', '已通过'].includes(event.extendedProps.status)
}
</script>

<style scoped>
/* 保持原有样式不变 */
.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.calendar-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.25);
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

/* 工具栏背景装饰 */
.calendar-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  pointer-events: none;
  overflow: hidden;
  border-radius: 12px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.current-date {
  font-size: 22px;
  font-weight: 700;
  color: white;
  margin: 0 24px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.calendar-main {
  flex: 1;
  height: calc(100vh - 140px);
  background: #f8fafc;
}

.calendar-content {
  height: 100%;
  padding: 24px;
  background: #f8fafc;
  overflow: hidden;
}

/* FullCalendar 主容器美化 */
:deep(.fc) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background: white;
  border-radius: 16px;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 表格边框优化 */
:deep(.fc-theme-standard td, .fc-theme-standard th) {
  border: 1px solid #f1f5f9;
}

:deep(.fc-theme-standard .fc-scrollgrid) {
  border: none;
}

/* 今天的样式 - 更加突出 */
:deep(.fc-day-today) {
  background: linear-gradient(135deg, #fef3ff 0%, #f0f9ff 100%) !important;
  position: relative;
}

:deep(.fc-day-today::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(99, 102, 241, 0.08) 100%);
  pointer-events: none;
}

/* 日期数字美化 */
:deep(.fc-daygrid-day-number) {
  color: #475569;
  font-weight: 600;
  font-size: 15px;
  padding: 10px 12px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  margin: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

:deep(.fc-daygrid-day:hover .fc-daygrid-day-number) {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 16px rgba(99, 102, 241, 0.3);
}

:deep(.fc-day-today .fc-daygrid-day-number) {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
  position: relative;
  z-index: 2;
}

/* 表头样式 */
:deep(.fc-col-header-cell) {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 700;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  font-size: 20px;
  padding: 20px 8px;
  border-bottom: 2px solid #e2e8f0;
  position: relative;
}

:deep(.fc-col-header-cell::after) {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
    height: 2px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 1px;
}

:deep(.fc-col-header-cell-cushion) {
  padding: 0;
}

/* 日期单元格 */
:deep(.fc-daygrid-day-frame) {
  min-height: 120px;
  padding: 8px;
  position: relative;
  transition: all 0.3s ease;
}

:deep(.fc-daygrid-day) {
  transition: all 0.3s ease;
}

:deep(.fc-daygrid-day:hover) {
  background: linear-gradient(135deg, #fefbff 0%, #f0f9ff 100%);
  /* transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); */
}

/* 事件样式大幅美化 */
:deep(.fc-daygrid-event) {
  border-radius: 8px;
  margin: 2px 1px;
  font-size: 12px;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

:deep(.fc-daygrid-event::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
}

:deep(.fc-daygrid-event:hover) {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

:deep(.fc-event-title) {
  font-weight: 600;
  padding: 6px 10px;
  line-height: 1.3;
}

:deep(.fc-event-time) {
  font-weight: 500;
  opacity: 0.9;
  font-size: 11px;
}

/* 更多链接美化 */
:deep(.fc-more-link) {
  color: #6366f1;
  font-size: 11px;
  font-weight: 600;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 6px;
  padding: 4px 8px;
  margin: 2px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

:deep(.fc-more-link:hover) {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  transform: scale(1.05);
  border-color: rgba(99, 102, 241, 0.4);
}

/* 弹窗美化 */
:deep(.fc-popover) {
  border: none;
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

:deep(.fc-popover-header) {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  font-weight: 700;
  padding: 16px 20px;
  font-size: 14px;
}

:deep(.fc-popover-body) {
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
}

/* 周末样式 */
:deep(.fc-day-sat, .fc-day-sun) {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
}

:deep(.fc-day-sat .fc-daygrid-day-number, .fc-day-sun .fc-daygrid-day-number) {
  color: #d97706;
}

/* 过去的日期 */
:deep(.fc-day-past) {
  opacity: 0.6;
  position: relative;
}

:deep(.fc-day-past::after) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(0, 0, 0, 0.02) 50%, transparent 60%);
  pointer-events: none;
}

/* 工具栏按钮美化 - 适配若依风格 */
:deep(.el-button-group .el-button) {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button-group .el-button:hover) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  color: white;
  transform: translateY(-1px);
}

:deep(.el-button-group .el-button.is-active) {
  background: white;
  color: #6366f1;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 选择器美化 */
:deep(.el-select .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-select .el-input__wrapper:hover) {
  border-color: rgba(255, 255, 255, 0.4);
}

/* 新建预约按钮特殊样式 */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

/* 选中高亮 */
:deep(.fc-highlight) {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(139, 92, 246, 0.15)) !important;
  border-radius: 8px;
}

/* 加载状态 */
:deep(.fc-loading) {
  opacity: 0.7;
  position: relative;
}

:deep(.fc-loading::after) {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.day-events-list {
  max-height: 400px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8fafc;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid #6366f1;
}

.event-item:hover {
  background: #e0e7ff;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.event-time {
  font-weight: 600;
  color: #6366f1;
  min-width: 80px;
  font-size: 14px;
}

.event-title {
  flex: 1;
  font-weight: 500;
  color: #1e293b;
  margin: 0 12px;
}

.event-room {
  color: #64748b;
  font-size: 12px;
  background: white;
  padding: 2px 8px;
  border-radius: 4px;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .current-date {
    font-size: 18px;
  }
  
  .calendar-toolbar {
    padding: 16px 20px;
  }
  
  :deep(.fc-daygrid-day-frame) {
    min-height: 100px;
  }
}

@media (max-width: 768px) {
  .toolbar-right .current-date {
    display: none;
  }
  
  .calendar-toolbar {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }
  
  .toolbar-left, .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  :deep(.fc-daygrid-day-frame) {
    min-height: 80px;
  }
  
  :deep(.fc-daygrid-day-number) {
    font-size: 14px;
    min-width: 28px;
    min-height: 28px;
  }
}

/* 微妙的动画效果 */
:deep(.fc-daygrid-event) {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条 */
:deep(.fc-scroller::-webkit-scrollbar) {
  width: 6px;
}

:deep(.fc-scroller::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 3px;
}

:deep(.fc-scroller::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 3px;
}

:deep(.fc-scroller::-webkit-scrollbar-thumb:hover) {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
}
</style>
